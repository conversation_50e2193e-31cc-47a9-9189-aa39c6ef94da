import React, { useCallback, useMemo } from 'react';
import {
  get,
  isEqual,
  map,
  pick,
  some,
  filter,
  isUndefined,
  isString,
  isEmpty,
  isArray,
} from 'lodash';
import { useQuery } from 'react-apollo';
import classnames from 'classnames';

import useT from '../../../../../../../common/components/utils/Translations/useT';
import EntityForm from '../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import StatusWithDraftField from '../../../../../../../common/components/containers/EntityForm/fields/StatusWithDraftField';
import IEContentFolder from '../../../../../../../common/abstract/EContent/IEContentFolder';
import IconSelectorField from '../../../../../../../common/components/containers/EntityForm/fields/IconSelectorField';
import StatusWithDraft, {
  Draft,
} from '../../../../../../../model/StatusWithDraft';
import {
  IRootNode,
  isRootNode,
} from '../../../../../../../common/components/dataViews/DynamicTree/constants';
import { isEContentLibraryFolder } from '../../../../../../../model/EContentLibraryResourcesTypeNames';
import EContentLibraryResourcesTreeSelectorField from '../../../../../../../common/components/containers/EntityForm/fields/EContentLibraryResourcesTreeSelectorField';
import InlineAttachmentFieldWithPreview from '../../../../../../../common/components/containers/EntityForm/fields/InlineAttachmentsFilePreview';
import { E_CONTENT_FILE } from '../../../../../../../fsCategories';
import IEContentResource from '../../../../../../../common/abstract/EContent/IEContentResource';
import TextAreaField from '../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import Strike from '../../../../../../../common/components/utils/Strike';
import SequenceField from '../../../../../../../common/components/containers/EntityForm/fields/SequenceField';
import attributeTypes from '../../../../../../../model/EContentResourceXAttributeTypes';
import EContentResourceXAttributeSubSection from './EContentResourceXAttributeSubSection';
import EContentResourceXAttributeTexts from './EContentResourceXAttributeTexts';
import EContentResourceXAttributeDocuments from './EContentResourceXAttributeDocuments';
import EContentResourceXAttributeImages from './EContentResourceXAttributeImages';
import EContentResourceXAttributeVideoFiles from './EContentResourceXAttributeVideoFiles';
import EContentResourceXAttributeAudioFiles from './EContentResourceXAttributeAudioFiles';
import EContentResourceXAttributeUrls from './EContentResourceXAttributeUrls';
import EContentResourceXAttributeQuestion from './EContentResourceXAttributeQuestion';
import { IEContentResourceXAttribute } from '../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';
import LanguageMultiSelectField from '../../../../../../../common/components/containers/EntityForm/fields/LanguageMultiSelectField';
import cookEContentResourceXAttributes from '../../../../../../../common/utils/cookEContentResourceXAttributes';
import { Simple } from '../../../../../../../model/TextsType';

import nextEContentResourceSequence from '../../../../../../../common/data/eContent/nextEContentResourceSequence.graphql';
import getGqlOperationName from '../../../../../../../common/utils/getGqlOperationName';
import Notifications from '../../../../../../../common/utils/Notifications';
import EContentResourceXAttributeFeatures from './EContentResourceXAttributeFeatures';
import styles from './styles.scss';
import EContentResourceXAttributeRoleAssignment from './EContentResourceXAttributeRoleAssignment';
import IEContentLibrary, {
  IEcnRoleItem,
} from '../../../../../../../common/abstract/EContent/IEContentLibrary';

export const CHARACTERS_DEFAULT = 100;
export const MAXIMUM_CHARACTERS_DEFAULT = 500;

export interface IEContentResourceEntity extends IEContentResource {
  cookedAttributes?: Record<string, IEContentResourceXAttribute>;
  ecnRoles?: IEcnRoleItem[];
  ecnRoleXPersons?: any[];
}
export interface IEContentResourceForm {
  node: IEContentResourceEntity;
  onCancel: () => void;
  onSubmit: (values: IEContentResourceEntity) => Promise<void>;
  library: IEContentLibrary;
  libraryId: number;
  parentNode: IRootNode | IEContentFolder;
  libraryName: string;
  organisationGroupId: number;
}
const EContentResourceForm: React.FC<IEContentResourceForm> = ({
  node,
  onCancel,
  onSubmit,
  library,
  libraryId,
  parentNode,
  libraryName,
  organisationGroupId,
}) => {
  const t = useT();
  const isNew = useMemo(() => !node?.id, [node]);

  const { loading, data } = useQuery(nextEContentResourceSequence, {
    variables: {
      libraryId,
      folderId: isRootNode(parentNode) ? null : parentNode?.id,
    },
  });

  const nextSequence = useMemo(
    () => get(data, getGqlOperationName(nextEContentResourceSequence), 1),
    [data],
  );

  const cookSubmitData = useCallback(
    ({
      parentLevel,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      parent,
      attachments,
      cookedAttributes: {
        topicKeyword,
        contentKeyword,
        contentCategory,
        source,
        author,
        text,
        minNumberOfTextBoxes,
        maxNumberOfTextBoxes,
        textCharactersNumber,
        textHeight,
        previewRenderingSimpleText,
        previewRenderingRichText,
        document,
        minNumberOfDocuments,
        maxNumberOfDocuments,
        maxSizeOfEachDocument,
        documentCaption,
        image,
        minNumberOfImages,
        maxNumberOfImages,
        maxSizeOfEachImage,
        imageCaption,
        videoFile,
        minNumberOfVideoFiles,
        maxNumberOfVideoFiles,
        maxSizeOfEachVideoFile,
        videoFileLength,
        videoFileCaption,
        audioFile,
        minNumberOfAudioFiles,
        maxNumberOfAudioFiles,
        maxSizeOfEachAudioFile,
        audioFileLength,
        audioFileCaption,
        audioValidation,
        pitch,
        frequency,
        noise,
        loudness,
        url,
        minNumberOfUrls,
        maxNumberOfUrls,
        urlCaption,
        textKeyword,
        documentKeyword,
        imageKeyword,
        imageAiImprovement,
        videoFileKeyword,
        audioFileKeyword,
        urlName,
        urlKeyword,
        question,
        minNumberOfQuestion,
        maxNumberOfQuestion,
        questionType,
        questionTitle,
        questionImage,
        questionMultiple,
        questionLevel,
        questionPoint,
        questionKeyword,
        answerMaxLimit,
        questionCorrectAnswer,
        translationSupport,
        transform,
        aiQuestions,
        textToAudio,
      },
      ...values
    }) => {
      const features = [
        resolveAttribute(textToAudio, {
          attributeId: attributeTypes.BasicByName.textToAudio?.id,
          isChecked: values.features.includes(
            attributeTypes.BasicByName.textToAudio?.id,
          ),
        }),
        resolveAttribute(translationSupport, {
          attributeId: attributeTypes.BasicByName.translationSupport?.id,
          isChecked: values.features.includes(
            attributeTypes.BasicByName.translationSupport?.id,
          ),
        }),
        resolveAttribute(transform, {
          attributeId: attributeTypes.BasicByName.transform?.id,
          isChecked: values.features.includes(
            attributeTypes.BasicByName.transform?.id,
          ),
        }),
        resolveAttribute(aiQuestions, {
          attributeId: attributeTypes.BasicByName.aiQuestions?.id,
          isChecked: values.features.includes(
            attributeTypes.BasicByName.aiQuestions?.id,
          ),
        }),
      ];

      const baseAttributes = filter(
        [
          resolveAttribute(topicKeyword, {
            attributeId: attributeTypes.BasicByName.topicKeyword?.id,
          }),
          resolveAttribute(contentKeyword, {
            attributeId: attributeTypes.BasicByName.contentKeyword?.id,
          }),
          resolveAttribute(contentCategory, {
            attributeId: attributeTypes.BasicByName.contentCategory?.id,
          }),
          resolveAttribute(source, {
            attributeId: attributeTypes.BasicByName.source?.id,
          }),
          resolveAttribute(author, {
            attributeId: attributeTypes.BasicByName.author?.id,
          }),
        ],
        x => !isUndefined(x?.isChecked),
      );

      return {
        ...values,
        organisationGroupId,
        folderId: isRootNode(parentLevel) ? null : parentLevel?.id,
        attachments: map(attachments, a =>
          pick(a, ['description', 'fileName', 'fileId', 'uploadToken']),
        ),
        attributes: filter([
          ...features,
          ...baseAttributes,

          ...resolveText(),
          ...resolveDocuments(),
          ...resolveImages(),
          ...resolveVideos(),
          ...resolveAudios(),
          ...resolveUrls(),
          ...resolveQuestion(),
        ]),
      };

      function resolveText() {
        const mainAttribute = resolveAttribute(text, {
          attributeId: attributeTypes.BasicByName.text?.id,
        });
        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,
            resolveAttribute(minNumberOfTextBoxes, {
              attributeId: attributeTypes.BasicByName.minNumberOfTextBoxes?.id,
              isChecked: text?.isChecked,
            }),
            resolveAttribute(maxNumberOfTextBoxes, {
              attributeId: attributeTypes.BasicByName.maxNumberOfTextBoxes?.id,
              isChecked: text?.isChecked,
            }),
            resolveAttribute(textCharactersNumber, {
              attributeId: attributeTypes.BasicByName.textCharactersNumber?.id,
              isChecked: text?.isChecked || text?.value === Simple.value,
            }),
            resolveAttribute(textHeight, {
              attributeId: attributeTypes.BasicByName.textHeight?.id,
              isChecked: text?.isChecked,
            }),
            resolveAttribute(previewRenderingSimpleText, {
              attributeId:
                attributeTypes.BasicByName.previewRenderingSimpleText?.id,
              isChecked: previewRenderingSimpleText?.isChecked,
            }),
            resolveAttribute(previewRenderingRichText, {
              attributeId:
                attributeTypes.BasicByName.previewRenderingRichText?.id,
              isChecked: previewRenderingRichText?.isChecked,
            }),
            resolveAttribute(
              textKeyword,
              {
                attributeId: attributeTypes.BasicByName.textKeyword?.id,
                isChecked: text?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
          ];

          return result;
        }

        return [];
      }

      function resolveDocuments() {
        const mainAttribute = resolveAttribute(document, {
          attributeId: attributeTypes.BasicByName.document?.id,
        });

        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,
            resolveAttribute(minNumberOfDocuments, {
              attributeId: attributeTypes.BasicByName.minNumberOfDocuments?.id,
              isChecked: document?.isChecked,
            }),
            resolveAttribute(maxNumberOfDocuments, {
              attributeId: attributeTypes.BasicByName.maxNumberOfDocuments?.id,
              isChecked: document?.isChecked,
            }),
            resolveAttribute(maxSizeOfEachDocument, {
              attributeId: attributeTypes.BasicByName.maxSizeOfEachDocument?.id,
              isChecked: document?.isChecked,
            }),
            resolveAttribute(documentCaption, {
              attributeId: attributeTypes.BasicByName.documentCaption?.id,
              isChecked: document?.isChecked && documentCaption?.isChecked,
            }),
            resolveAttribute(
              documentKeyword,
              {
                attributeId: attributeTypes.BasicByName.documentKeyword?.id,
                isChecked: document?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
          ];

          return result;
        }

        return [];
      }

      function resolveImages() {
        const mainAttribute = resolveAttribute(image, {
          attributeId: attributeTypes.BasicByName.image?.id,
        });

        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,
            resolveAttribute(minNumberOfImages, {
              attributeId: attributeTypes.BasicByName.minNumberOfImages?.id,
              isChecked: image?.isChecked,
            }),
            resolveAttribute(maxNumberOfImages, {
              attributeId: attributeTypes.BasicByName.maxNumberOfImages?.id,
              isChecked: image?.isChecked,
            }),
            resolveAttribute(maxSizeOfEachImage, {
              attributeId: attributeTypes.BasicByName.maxSizeOfEachImage?.id,
              isChecked: image?.isChecked,
            }),
            resolveAttribute(imageCaption, {
              attributeId: attributeTypes.BasicByName.imageCaption?.id,
              isChecked: image?.isChecked && imageCaption?.isChecked,
            }),
            resolveAttribute(
              imageKeyword,
              {
                attributeId: attributeTypes.BasicByName.imageKeyword?.id,
                isChecked: image?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
            resolveAttribute(imageAiImprovement, {
              attributeId: attributeTypes.BasicByName.imageAiImprovement?.id,
              isChecked: image?.isChecked && imageAiImprovement?.isChecked,
            }),
          ];

          return result;
        }

        return [];
      }

      function resolveVideos() {
        const mainAttribute = resolveAttribute(videoFile, {
          attributeId: attributeTypes.BasicByName.videoFile?.id,
        });

        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,
            resolveAttribute(minNumberOfVideoFiles, {
              attributeId: attributeTypes.BasicByName.minNumberOfVideoFiles?.id,
              isChecked: videoFile?.isChecked,
            }),
            resolveAttribute(maxNumberOfVideoFiles, {
              attributeId: attributeTypes.BasicByName.maxNumberOfVideoFiles?.id,
              isChecked: videoFile?.isChecked,
            }),
            resolveAttribute(maxSizeOfEachVideoFile, {
              attributeId:
                attributeTypes.BasicByName.maxSizeOfEachVideoFile?.id,
              isChecked: videoFile?.isChecked,
            }),
            resolveAttribute(videoFileCaption, {
              attributeId: attributeTypes.BasicByName.videoFileCaption?.id,
              isChecked: videoFile?.isChecked && videoFileCaption?.isChecked,
            }),
            resolveAttribute(
              videoFileKeyword,
              {
                attributeId: attributeTypes.BasicByName.videoFileKeyword?.id,
                isChecked: videoFile?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
            resolveAttribute(
              videoFileLength,
              {
                attributeId: attributeTypes.BasicByName.videoFileLength?.id,
                isChecked: videoFile?.isChecked,
              },
              true,
            ),
          ];

          return result;
        }
        return [];
      }

      function resolveAudios() {
        const mainAttribute = resolveAttribute(audioFile, {
          attributeId: attributeTypes.BasicByName.audioFile?.id,
        });

        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,
            resolveAttribute(minNumberOfAudioFiles, {
              attributeId: attributeTypes.BasicByName.minNumberOfAudioFiles?.id,
              isChecked: audioFile?.isChecked,
            }),
            resolveAttribute(maxNumberOfAudioFiles, {
              attributeId: attributeTypes.BasicByName.maxNumberOfAudioFiles?.id,
              isChecked: audioFile?.isChecked,
            }),
            resolveAttribute(maxSizeOfEachAudioFile, {
              attributeId:
                attributeTypes.BasicByName.maxSizeOfEachAudioFile?.id,
              isChecked: audioFile?.isChecked,
            }),
            resolveAttribute(audioFileCaption, {
              attributeId: attributeTypes.BasicByName.audioFileCaption?.id,
              isChecked: audioFile?.isChecked && audioFileCaption?.isChecked,
            }),
            resolveAttribute(
              audioFileKeyword,
              {
                attributeId: attributeTypes.BasicByName.audioFileKeyword?.id,
                isChecked: audioFile?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
            resolveAttribute(
              audioFileLength,
              {
                attributeId: attributeTypes.BasicByName.audioFileLength?.id,
                isChecked: audioFile?.isChecked,
              },
              true,
            ),
            resolveAttribute(audioValidation, {
              attributeId: attributeTypes.BasicByName.audioValidation?.id,
              isChecked: audioFile?.isChecked && audioValidation?.isChecked,
            }),
            resolveAttribute(pitch, {
              attributeId: attributeTypes.BasicByName.pitch?.id,
              isChecked: audioFile?.isChecked && audioValidation?.isChecked,
            }),
            resolveAttribute(frequency, {
              attributeId: attributeTypes.BasicByName.frequency?.id,
              isChecked: audioFile?.isChecked && audioValidation?.isChecked,
            }),
            resolveAttribute(noise, {
              attributeId: attributeTypes.BasicByName.noise?.id,
              isChecked: audioFile?.isChecked && audioValidation?.isChecked,
            }),
            resolveAttribute(loudness, {
              attributeId: attributeTypes.BasicByName.loudness?.id,
              isChecked: audioFile?.isChecked && audioValidation?.isChecked,
            }),
          ];

          return result;
        }
        return [];
      }

      function resolveUrls() {
        const mainAttribute = resolveAttribute(url, {
          attributeId: attributeTypes.BasicByName.url?.id,
        });

        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,

            resolveAttribute(minNumberOfUrls, {
              attributeId: attributeTypes.BasicByName.minNumberOfUrls?.id,
              isChecked: url?.isChecked,
            }),
            resolveAttribute(maxNumberOfUrls, {
              attributeId: attributeTypes.BasicByName.maxNumberOfUrls?.id,
              isChecked: url?.isChecked,
            }),

            resolveAttribute(urlCaption, {
              attributeId: attributeTypes.BasicByName.urlCaption?.id,
              isChecked: url?.isChecked && urlCaption?.isChecked,
            }),

            resolveAttribute(
              urlName,
              {
                attributeId: attributeTypes.BasicByName.urlName?.id,
                isChecked: url?.isChecked,
              },
              true,
            ),

            resolveAttribute(
              urlKeyword,
              {
                attributeId: attributeTypes.BasicByName.urlKeyword?.id,
                isChecked: url?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
          ];
          return result;
        }
        return [];
      }

      function resolveQuestion() {
        const mainAttribute = resolveAttribute(question, {
          attributeId: attributeTypes.BasicByName.question?.id,
        });

        const isNew = !mainAttribute?.id;
        const isChecked = mainAttribute?.isChecked;

        if (!isNew || (isNew && isChecked)) {
          const result = [
            mainAttribute,
            resolveAttribute(minNumberOfQuestion, {
              attributeId: attributeTypes.BasicByName.minNumberOfQuestion?.id,
              isChecked: question?.isChecked,
            }),
            resolveAttribute(maxNumberOfQuestion, {
              attributeId: attributeTypes.BasicByName.maxNumberOfQuestion?.id,
              isChecked: question?.isChecked,
            }),
            resolveAttribute(
              questionType,
              {
                attributeId: attributeTypes.BasicByName.questionType?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionTitle,
              {
                attributeId: attributeTypes.BasicByName.questionTitle?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionImage,
              {
                attributeId: attributeTypes.BasicByName.questionImage?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionMultiple,
              {
                attributeId: attributeTypes.BasicByName.questionMultiple?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionLevel,
              {
                attributeId: attributeTypes.BasicByName.questionLevel?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionPoint,
              {
                attributeId: attributeTypes.BasicByName.questionPoint?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              answerMaxLimit,
              {
                attributeId: attributeTypes.BasicByName.answerMaxLimit?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionCorrectAnswer,
              {
                attributeId:
                  attributeTypes.BasicByName.questionCorrectAnswer?.id,
                isChecked: question?.isChecked,
              },
              true,
            ),
            resolveAttribute(
              questionKeyword,
              {
                attributeId: attributeTypes.BasicByName.questionKeyword?.id,
                isChecked: question?.isChecked && contentKeyword?.isChecked,
              },
              !!contentKeyword?.isChecked,
            ),
          ];
          return result;
        }
        return [];
      }
      function resolveAttribute(
        attribute,
        defaultValues,
        hasResolvedAnyway = false,
      ) {
        if (hasResolvedAnyway) {
          return { ...(attribute || {}), ...defaultValues };
        }

        if (!attribute) {
          return null;
        }
        if (
          attribute.attributeId ===
          attributeTypes.BasicByName.maxNumberOfTextBoxes?.id
        ) {
          if (isString(attribute.value)) {
            const attr = {
              ...attribute,
              value: Number(attribute.value),
            };
            return { ...attr, ...defaultValues };
          }
        }
        if (
          defaultValues.attributeId ===
          attributeTypes.BasicByName.contentCategory?.id
        ) {
          if (isArray(attribute.categories)) {
            const attr = {
              ...attribute,
              value: undefined,
            };
            return {
              ...attr,
              categories: attribute.categories?.filter(x => !isEmpty(x.name)),
              ...defaultValues,
            };
          }
        }
        return { ...attribute, ...defaultValues };
      }
    },
    [organisationGroupId],
  );

  const _onSubmit = useCallback(
    values => {
      const { cookedAttributes } = values;
      const {
        text,
        document,
        image,
        videoFile,
        audioFile,
        url,
        question,
      } = cookedAttributes;
      const isAnyContentSelected = some([
        text?.isChecked,
        document?.isChecked,
        image?.isChecked,
        videoFile?.isChecked,
        audioFile?.isChecked,
        url?.isChecked,
        question?.isChecked,
      ]);

      if (!isAnyContentSelected) {
        Notifications.error(
          t('Error'),
          'Choose at least one type of content for the resource',
          t,
        );
        return false;
      }
      if (text && text.isChecked && text.value === 0) {
        Notifications.error(
          t('Error'),
          'Choose at least one type for the text',
          t,
        );
        return false;
      }
      const isContentCategoryChecked =
        values.cookedAttributes.contentCategory?.isChecked || false;
      const categories =
        values.cookedAttributes.contentCategory?.categories?.filter(
          x => !isEmpty(x.name),
        ) || [];

      if (isContentCategoryChecked && isEmpty(categories)) {
        Notifications.error(
          t('Error'),
          'Add at least one category for the content',
          t,
        );
        return false;
      }

      const submitData = cookSubmitData(values);
      const { features, ecnRoleXPersons, ...data } = submitData;
      return onSubmit(data);
    },
    [t, cookSubmitData, onSubmit],
  );

  const rootNode = useMemo(
    () => ({
      id: ':ROOT:',
      name: libraryName,
    }),
    [libraryName],
  );

  const hasProcessManagement = useMemo(
    () => library.hasProcessManagement || false,
    [library],
  );

  const _entity = useMemo(() => {
    if (isNew) {
      const { ecnRoles = [] } = library;
      const _ecnRoles = ecnRoles.map(item => ({
        ...item,
        people: [],
      }));

      return {
        libraryId,
        languageIds: [],
        sequence: nextSequence,
        status: Draft.value,
        features: [],
        parentLevel: isEContentLibraryFolder(parentNode)
          ? parentNode
          : rootNode,
        cookedAttributes: {
          ...defaultAttributes,
        },
        ecnRoles: _ecnRoles,
      };
    }

    const { attributes, ecnRoles = [], ecnRoleXPersons = [], ...rest } = node;

    const _ecnRoles = ecnRoles.map(item => ({
      ...item,
      people:
        map(
          ecnRoleXPersons.filter(x => x.roleId === item.id),
          'personId',
        ) || [],
    }));

    return {
      ...rest,
      ecnRoles: _ecnRoles,
      parentLevel: node?.parent ?? rootNode,
      features: map(
        attributes?.filter(
          x =>
            (x.status === StatusWithDraft.Active.value &&
              x.attributeId === attributeTypes.BasicByName.textToAudio?.id) ||
            (x.status === StatusWithDraft.Active.value &&
              x.attributeId ===
                attributeTypes.BasicByName.translationSupport?.id) ||
            (x.status === StatusWithDraft.Active.value &&
              x.attributeId === attributeTypes.BasicByName.transform?.id) ||
            (x.status === StatusWithDraft.Active.value &&
              x.attributeId === attributeTypes.BasicByName.aiQuestions?.id),
        ),
        'attributeId',
      ),
      cookedAttributes: {
        ...defaultAttributes,
        ...cookEContentResourceXAttributes(attributes),
      },
    };
  }, [isNew, node, libraryId, parentNode, rootNode, nextSequence, library]);

  const isSubmitDisabled = useCallback(
    (isSubmitting, formikBag) => {
      const { values, initialValues } = formikBag;
      return (
        isSubmitting ||
        (!isNew &&
          !isEmpty(values) &&
          !isEmpty(initialValues) &&
          isEqual(cookSubmitData(initialValues), cookSubmitData(values)))
      );
    },
    [isNew, cookSubmitData],
  );

  return (
    <EntityForm
      entity={_entity as IEContentResource}
      hasValidateOnBlur={false}
      hasValidateOnChange={false}
      isSubmitDisabled={isSubmitDisabled}
      onCancel={isNew ? onCancel : undefined}
      onGoBack={isNew ? undefined : onCancel}
      onSubmit={_onSubmit}
    >
      <EntityFormFieldSet>
        <EntityNameField label={t('Resource name')} />
        <LanguageMultiSelectField
          columns={3}
          wrapperClassNames={{
            3: classnames('col-lg-4 col-md-6 col-sm-12', styles.mb12),
          }}
        />
        <IconSelectorField label={t('Icon')} name="icon" />
        <EntityNameField
          label={t('Content Heading Label')}
          name="contentHeadingLabel"
        />
        <EntityNameField
          label={t('Content Subheading Label')}
          name="contentSubHeadingLabel"
          required={false}
        />
        <EContentLibraryResourcesTreeSelectorField
          gqlVariables={{ treeDataMode: 'EDIT' }}
          libraryId={libraryId}
          syntheticRootNodeName={libraryName}
        />
        <SequenceField
          required
          defaultValue={nextSequence}
          label={t('Sequence')}
          loading={loading}
        />
        <StatusWithDraftField label={t('Status')} />
        <TextAreaField
          columns={1}
          label={t('Description')}
          name="description"
        />
      </EntityFormFieldSet>
      <EntityFormFieldSet className="pl-10 pr-10 mb-5">
        <h6>{t('Graphic Image')}</h6>
      </EntityFormFieldSet>
      <InlineAttachmentFieldWithPreview
        categoryKey={E_CONTENT_FILE}
        isMultiple={false}
        organisationGroupId={organisationGroupId}
      />
      <Strike isSubsection title={t('Framework')} />
      <EContentResourceXAttributeSubSection
        hasMaximumCharactersFields
        columns={3}
        label={t('Topic Keyword')}
        name="topicKeyword"
      />
      <EContentResourceXAttributeSubSection
        hasMaximumCharactersFields
        columns={3}
        label={t('Content Keyword')}
        name="contentKeyword"
      />
      <EContentResourceXAttributeSubSection
        hasCategoryOptions
        columns={3}
        hasRuleField={false}
        label={t('Content Category')}
        name="contentCategory"
      />
      <EContentResourceXAttributeSubSection
        columns={3}
        label={t('Source')}
        name="source"
      />
      <EContentResourceXAttributeSubSection
        columns={3}
        label={t('Author')}
        name="author"
      />
      <Strike isSubsection title={t('Content')} />
      <EContentResourceXAttributeTexts />
      <EContentResourceXAttributeDocuments />
      <EContentResourceXAttributeImages />
      <EContentResourceXAttributeVideoFiles />
      <EContentResourceXAttributeAudioFiles />
      <EContentResourceXAttributeUrls />
      <EContentResourceXAttributeQuestion />
      <EContentResourceXAttributeFeatures />
      {hasProcessManagement && (
        <>
          <Strike isSubsection className="mt-10" title={t('Role Assignment')} />
          <EContentResourceXAttributeRoleAssignment />
        </>
      )}
    </EntityForm>
  );
};

export default EContentResourceForm;

const defaultAttributes = {
  topicKeyword: {
    value: 100,
  },
  contentKeyword: {
    value: 100,
  },
  text: {
    isChecked: false,
    value: Simple.value,
  },
  minNumberOfVideoFiles: {
    value: 1,
  },
  maxNumberOfVideoFiles: {
    value: 1,
  },
  minNumberOfUrls: {
    value: 1,
  },
  maxNumberOfUrls: {
    value: 1,
  },
  minNumberOfQuestion: {
    value: 1,
  },
  maxNumberOfQuestion: {
    value: 1,
  },
  minNumberOfTextBoxes: {
    value: 1,
  },
  maxNumberOfTextBoxes: {
    value: 1,
  },
  textCharactersNumber: {
    value: 300,
  },
  textHeight: {
    value: 20,
  },
  previewRenderingSimpleText: {
    isChecked: false,
  },
  previewRenderingRichText: {
    isChecked: false,
  },
  minNumberOfImages: {
    value: 1,
  },
  maxNumberOfImages: {
    value: 1,
  },
  minNumberOfDocuments: {
    value: 1,
  },
  maxNumberOfDocuments: {
    value: 1,
  },
  maxSizeOfEachDocument: {
    value: 5,
  },
  minNumberOfAudioFiles: {
    value: 1,
  },
  maxNumberOfAudioFiles: {
    value: 1,
  },
  translationSupport: {
    isChecked: false,
  },
  transform: {
    isChecked: false,
  },
  aiQuestions: {
    isChecked: false,
  },
  textToAudio: {
    isChecked: false,
  },
  audioValidation: {
    isChecked: false,
  },
  pitch: {
    value: 4000,
  },
  frequency: {
    value: 200,
  },
  noise: {
    value: 25,
  },
  loudness: {
    value: 90,
  },
};
